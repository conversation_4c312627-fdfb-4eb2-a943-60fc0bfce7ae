/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/documents/[documentId]/route";
exports.ids = ["app/api/documents/[documentId]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute&page=%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute&page=%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_documents_documentId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/documents/[documentId]/route.ts */ \"(rsc)/./src/app/api/documents/[documentId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/documents/[documentId]/route\",\n        pathname: \"/api/documents/[documentId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/documents/[documentId]/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\documents\\\\[documentId]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_documents_documentId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute&page=%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/documents/[documentId]/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/documents/[documentId]/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function DELETE(request, { params }) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        // Check authentication\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { documentId } = await params;\n        if (!documentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Document ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // First, get the document to ensure it exists and belongs to the user\n        const { data: document, error: fetchError } = await supabase.from('documents').select('id, custom_api_config_id').eq('id', documentId).eq('user_id', user.id).single();\n        if (fetchError || !document) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Document not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        // Delete document chunks first (due to foreign key constraint)\n        const { error: chunksError } = await supabase.from('document_chunks').delete().eq('document_id', documentId).eq('user_id', user.id);\n        if (chunksError) {\n            console.error('[Document Delete] Error deleting chunks:', chunksError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to delete document chunks',\n                details: chunksError.message\n            }, {\n                status: 500\n            });\n        }\n        // Delete the document\n        const { error: docError } = await supabase.from('documents').delete().eq('id', documentId).eq('user_id', user.id);\n        if (docError) {\n            console.error('[Document Delete] Error deleting document:', docError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to delete document',\n                details: docError.message\n            }, {\n                status: 500\n            });\n        }\n        // Invalidate training cache to ensure immediate effect\n        try {\n            const { trainingDataCache } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_cache_trainingCache_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/cache/trainingCache */ \"(rsc)/./src/lib/cache/trainingCache.ts\"));\n            trainingDataCache.invalidate(document.custom_api_config_id);\n            console.log(`[Document Delete] Cache invalidated for config: ${document.custom_api_config_id}`);\n        } catch (error) {\n            console.warn('[Document Delete] Cache invalidation failed:', error);\n        }\n        console.log(`[Document Delete] Successfully deleted document: ${documentId}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'Document deleted successfully'\n        });\n    } catch (error) {\n        console.error('[Document Delete] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to delete document',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request, { params }) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        // Check authentication\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { documentId } = await params;\n        if (!documentId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Document ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Fetch document details\n        const { data: document, error } = await supabase.from('documents').select(`\n        id,\n        filename,\n        file_type,\n        file_size,\n        status,\n        metadata,\n        created_at,\n        updated_at\n      `).eq('id', documentId).eq('user_id', user.id).single();\n        if (error || !document) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Document not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        // Get chunk count\n        const { count } = await supabase.from('document_chunks').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('document_id', documentId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            document: {\n                ...document,\n                chunks_count: count || 0\n            }\n        });\n    } catch (error) {\n        console.error('[Document Get] Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch document',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9kb2N1bWVudHMvW2RvY3VtZW50SWRdL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0Q7QUFDb0I7QUFFckUsZUFBZUUsT0FDcEJDLE9BQW9CLEVBQ3BCLEVBQUVDLE1BQU0sRUFBK0M7SUFFdkQsSUFBSTtRQUNGLE1BQU1DLFdBQVcsTUFBTUoseUZBQW1DQTtRQUUxRCx1QkFBdUI7UUFDdkIsTUFBTSxFQUFFSyxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFQyxPQUFPQyxTQUFTLEVBQUUsR0FBRyxNQUFNSixTQUFTSyxJQUFJLENBQUNDLE9BQU87UUFDeEUsSUFBSUYsYUFBYSxDQUFDRixNQUFNO1lBQ3RCLE9BQU9QLHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7Z0JBQUVKLE9BQU87WUFBZSxHQUFHO2dCQUFFSyxRQUFRO1lBQUk7UUFDcEU7UUFFQSxNQUFNLEVBQUVDLFVBQVUsRUFBRSxHQUFHLE1BQU1WO1FBRTdCLElBQUksQ0FBQ1UsWUFBWTtZQUNmLE9BQU9kLHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7Z0JBQ3ZCSixPQUFPO1lBQ1QsR0FBRztnQkFBRUssUUFBUTtZQUFJO1FBQ25CO1FBRUEsc0VBQXNFO1FBQ3RFLE1BQU0sRUFBRVAsTUFBTVMsUUFBUSxFQUFFUCxPQUFPUSxVQUFVLEVBQUUsR0FBRyxNQUFNWCxTQUNqRFksSUFBSSxDQUFDLGFBQ0xDLE1BQU0sQ0FBQyw0QkFDUEMsRUFBRSxDQUFDLE1BQU1MLFlBQ1RLLEVBQUUsQ0FBQyxXQUFXWixLQUFLYSxFQUFFLEVBQ3JCQyxNQUFNO1FBRVQsSUFBSUwsY0FBYyxDQUFDRCxVQUFVO1lBQzNCLE9BQU9mLHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7Z0JBQ3ZCSixPQUFPO1lBQ1QsR0FBRztnQkFBRUssUUFBUTtZQUFJO1FBQ25CO1FBRUEsK0RBQStEO1FBQy9ELE1BQU0sRUFBRUwsT0FBT2MsV0FBVyxFQUFFLEdBQUcsTUFBTWpCLFNBQ2xDWSxJQUFJLENBQUMsbUJBQ0xNLE1BQU0sR0FDTkosRUFBRSxDQUFDLGVBQWVMLFlBQ2xCSyxFQUFFLENBQUMsV0FBV1osS0FBS2EsRUFBRTtRQUV4QixJQUFJRSxhQUFhO1lBQ2ZFLFFBQVFoQixLQUFLLENBQUMsNENBQTRDYztZQUMxRCxPQUFPdEIscURBQVlBLENBQUNZLElBQUksQ0FBQztnQkFDdkJKLE9BQU87Z0JBQ1BpQixTQUFTSCxZQUFZSSxPQUFPO1lBQzlCLEdBQUc7Z0JBQUViLFFBQVE7WUFBSTtRQUNuQjtRQUVBLHNCQUFzQjtRQUN0QixNQUFNLEVBQUVMLE9BQU9tQixRQUFRLEVBQUUsR0FBRyxNQUFNdEIsU0FDL0JZLElBQUksQ0FBQyxhQUNMTSxNQUFNLEdBQ05KLEVBQUUsQ0FBQyxNQUFNTCxZQUNUSyxFQUFFLENBQUMsV0FBV1osS0FBS2EsRUFBRTtRQUV4QixJQUFJTyxVQUFVO1lBQ1pILFFBQVFoQixLQUFLLENBQUMsOENBQThDbUI7WUFDNUQsT0FBTzNCLHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7Z0JBQ3ZCSixPQUFPO2dCQUNQaUIsU0FBU0UsU0FBU0QsT0FBTztZQUMzQixHQUFHO2dCQUFFYixRQUFRO1lBQUk7UUFDbkI7UUFFQSx1REFBdUQ7UUFDdkQsSUFBSTtZQUNGLE1BQU0sRUFBRWUsaUJBQWlCLEVBQUUsR0FBRyxNQUFNLDJNQUFtQztZQUN2RUEsa0JBQWtCQyxVQUFVLENBQUNkLFNBQVNlLG9CQUFvQjtZQUMxRE4sUUFBUU8sR0FBRyxDQUFDLENBQUMsZ0RBQWdELEVBQUVoQixTQUFTZSxvQkFBb0IsRUFBRTtRQUNoRyxFQUFFLE9BQU90QixPQUFPO1lBQ2RnQixRQUFRUSxJQUFJLENBQUMsZ0RBQWdEeEI7UUFDL0Q7UUFFQWdCLFFBQVFPLEdBQUcsQ0FBQyxDQUFDLGlEQUFpRCxFQUFFakIsWUFBWTtRQUU1RSxPQUFPZCxxREFBWUEsQ0FBQ1ksSUFBSSxDQUFDO1lBQ3ZCcUIsU0FBUztZQUNUUCxTQUFTO1FBQ1g7SUFFRixFQUFFLE9BQU9sQixPQUFZO1FBQ25CZ0IsUUFBUWhCLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzFDLE9BQU9SLHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7WUFDdkJKLE9BQU87WUFDUGlCLFNBQVNqQixNQUFNa0IsT0FBTztRQUN4QixHQUFHO1lBQUViLFFBQVE7UUFBSTtJQUNuQjtBQUNGO0FBRU8sZUFBZXFCLElBQ3BCL0IsT0FBb0IsRUFDcEIsRUFBRUMsTUFBTSxFQUErQztJQUV2RCxJQUFJO1FBQ0YsTUFBTUMsV0FBVyxNQUFNSix5RkFBbUNBO1FBRTFELHVCQUF1QjtRQUN2QixNQUFNLEVBQUVLLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUVDLE9BQU9DLFNBQVMsRUFBRSxHQUFHLE1BQU1KLFNBQVNLLElBQUksQ0FBQ0MsT0FBTztRQUN4RSxJQUFJRixhQUFhLENBQUNGLE1BQU07WUFDdEIsT0FBT1AscURBQVlBLENBQUNZLElBQUksQ0FBQztnQkFBRUosT0FBTztZQUFlLEdBQUc7Z0JBQUVLLFFBQVE7WUFBSTtRQUNwRTtRQUVBLE1BQU0sRUFBRUMsVUFBVSxFQUFFLEdBQUcsTUFBTVY7UUFFN0IsSUFBSSxDQUFDVSxZQUFZO1lBQ2YsT0FBT2QscURBQVlBLENBQUNZLElBQUksQ0FBQztnQkFDdkJKLE9BQU87WUFDVCxHQUFHO2dCQUFFSyxRQUFRO1lBQUk7UUFDbkI7UUFFQSx5QkFBeUI7UUFDekIsTUFBTSxFQUFFUCxNQUFNUyxRQUFRLEVBQUVQLEtBQUssRUFBRSxHQUFHLE1BQU1ILFNBQ3JDWSxJQUFJLENBQUMsYUFDTEMsTUFBTSxDQUFDLENBQUM7Ozs7Ozs7OztNQVNULENBQUMsRUFDQUMsRUFBRSxDQUFDLE1BQU1MLFlBQ1RLLEVBQUUsQ0FBQyxXQUFXWixLQUFLYSxFQUFFLEVBQ3JCQyxNQUFNO1FBRVQsSUFBSWIsU0FBUyxDQUFDTyxVQUFVO1lBQ3RCLE9BQU9mLHFEQUFZQSxDQUFDWSxJQUFJLENBQUM7Z0JBQ3ZCSixPQUFPO1lBQ1QsR0FBRztnQkFBRUssUUFBUTtZQUFJO1FBQ25CO1FBRUEsa0JBQWtCO1FBQ2xCLE1BQU0sRUFBRXNCLEtBQUssRUFBRSxHQUFHLE1BQU05QixTQUNyQlksSUFBSSxDQUFDLG1CQUNMQyxNQUFNLENBQUMsS0FBSztZQUFFaUIsT0FBTztZQUFTQyxNQUFNO1FBQUssR0FDekNqQixFQUFFLENBQUMsZUFBZUw7UUFFckIsT0FBT2QscURBQVlBLENBQUNZLElBQUksQ0FBQztZQUN2QnFCLFNBQVM7WUFDVGxCLFVBQVU7Z0JBQ1IsR0FBR0EsUUFBUTtnQkFDWHNCLGNBQWNGLFNBQVM7WUFDekI7UUFDRjtJQUVGLEVBQUUsT0FBTzNCLE9BQVk7UUFDbkJnQixRQUFRaEIsS0FBSyxDQUFDLHlCQUF5QkE7UUFDdkMsT0FBT1IscURBQVlBLENBQUNZLElBQUksQ0FBQztZQUN2QkosT0FBTztZQUNQaUIsU0FBU2pCLE1BQU1rQixPQUFPO1FBQ3hCLEdBQUc7WUFBRWIsUUFBUTtRQUFJO0lBQ25CO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxhcHBcXGFwaVxcZG9jdW1lbnRzXFxbZG9jdW1lbnRJZF1cXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBjcmVhdGVTdXBhYmFzZVNlcnZlckNsaWVudE9uUmVxdWVzdCB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL3NlcnZlcic7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBERUxFVEUoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogUHJvbWlzZTx7IGRvY3VtZW50SWQ6IHN0cmluZyB9PiB9XG4pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZVN1cGFiYXNlU2VydmVyQ2xpZW50T25SZXF1ZXN0KCk7XG4gICAgXG4gICAgLy8gQ2hlY2sgYXV0aGVudGljYXRpb25cbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9LCBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcbiAgICBpZiAoYXV0aEVycm9yIHx8ICF1c2VyKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfSwgeyBzdGF0dXM6IDQwMSB9KTtcbiAgICB9XG5cbiAgICBjb25zdCB7IGRvY3VtZW50SWQgfSA9IGF3YWl0IHBhcmFtcztcblxuICAgIGlmICghZG9jdW1lbnRJZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgXG4gICAgICAgIGVycm9yOiAnRG9jdW1lbnQgSUQgaXMgcmVxdWlyZWQnIFxuICAgICAgfSwgeyBzdGF0dXM6IDQwMCB9KTtcbiAgICB9XG5cbiAgICAvLyBGaXJzdCwgZ2V0IHRoZSBkb2N1bWVudCB0byBlbnN1cmUgaXQgZXhpc3RzIGFuZCBiZWxvbmdzIHRvIHRoZSB1c2VyXG4gICAgY29uc3QgeyBkYXRhOiBkb2N1bWVudCwgZXJyb3I6IGZldGNoRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnZG9jdW1lbnRzJylcbiAgICAgIC5zZWxlY3QoJ2lkLCBjdXN0b21fYXBpX2NvbmZpZ19pZCcpXG4gICAgICAuZXEoJ2lkJywgZG9jdW1lbnRJZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZmV0Y2hFcnJvciB8fCAhZG9jdW1lbnQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IFxuICAgICAgICBlcnJvcjogJ0RvY3VtZW50IG5vdCBmb3VuZCBvciBhY2Nlc3MgZGVuaWVkJyBcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDQgfSk7XG4gICAgfVxuXG4gICAgLy8gRGVsZXRlIGRvY3VtZW50IGNodW5rcyBmaXJzdCAoZHVlIHRvIGZvcmVpZ24ga2V5IGNvbnN0cmFpbnQpXG4gICAgY29uc3QgeyBlcnJvcjogY2h1bmtzRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgnZG9jdW1lbnRfY2h1bmtzJylcbiAgICAgIC5kZWxldGUoKVxuICAgICAgLmVxKCdkb2N1bWVudF9pZCcsIGRvY3VtZW50SWQpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKTtcblxuICAgIGlmIChjaHVua3NFcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW0RvY3VtZW50IERlbGV0ZV0gRXJyb3IgZGVsZXRpbmcgY2h1bmtzOicsIGNodW5rc0Vycm9yKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgIGVycm9yOiAnRmFpbGVkIHRvIGRlbGV0ZSBkb2N1bWVudCBjaHVua3MnLFxuICAgICAgICBkZXRhaWxzOiBjaHVua3NFcnJvci5tZXNzYWdlXG4gICAgICB9LCB7IHN0YXR1czogNTAwIH0pO1xuICAgIH1cblxuICAgIC8vIERlbGV0ZSB0aGUgZG9jdW1lbnRcbiAgICBjb25zdCB7IGVycm9yOiBkb2NFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdkb2N1bWVudHMnKVxuICAgICAgLmRlbGV0ZSgpXG4gICAgICAuZXEoJ2lkJywgZG9jdW1lbnRJZClcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpO1xuXG4gICAgaWYgKGRvY0Vycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdbRG9jdW1lbnQgRGVsZXRlXSBFcnJvciBkZWxldGluZyBkb2N1bWVudDonLCBkb2NFcnJvcik7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBlcnJvcjogJ0ZhaWxlZCB0byBkZWxldGUgZG9jdW1lbnQnLFxuICAgICAgICBkZXRhaWxzOiBkb2NFcnJvci5tZXNzYWdlXG4gICAgICB9LCB7IHN0YXR1czogNTAwIH0pO1xuICAgIH1cblxuICAgIC8vIEludmFsaWRhdGUgdHJhaW5pbmcgY2FjaGUgdG8gZW5zdXJlIGltbWVkaWF0ZSBlZmZlY3RcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyB0cmFpbmluZ0RhdGFDYWNoZSB9ID0gYXdhaXQgaW1wb3J0KCdAL2xpYi9jYWNoZS90cmFpbmluZ0NhY2hlJyk7XG4gICAgICB0cmFpbmluZ0RhdGFDYWNoZS5pbnZhbGlkYXRlKGRvY3VtZW50LmN1c3RvbV9hcGlfY29uZmlnX2lkKTtcbiAgICAgIGNvbnNvbGUubG9nKGBbRG9jdW1lbnQgRGVsZXRlXSBDYWNoZSBpbnZhbGlkYXRlZCBmb3IgY29uZmlnOiAke2RvY3VtZW50LmN1c3RvbV9hcGlfY29uZmlnX2lkfWApO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ1tEb2N1bWVudCBEZWxldGVdIENhY2hlIGludmFsaWRhdGlvbiBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGBbRG9jdW1lbnQgRGVsZXRlXSBTdWNjZXNzZnVsbHkgZGVsZXRlZCBkb2N1bWVudDogJHtkb2N1bWVudElkfWApO1xuXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBtZXNzYWdlOiAnRG9jdW1lbnQgZGVsZXRlZCBzdWNjZXNzZnVsbHknXG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgIGNvbnNvbGUuZXJyb3IoJ1tEb2N1bWVudCBEZWxldGVdIEVycm9yOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgZXJyb3I6ICdGYWlsZWQgdG8gZGVsZXRlIGRvY3VtZW50JyxcbiAgICAgIGRldGFpbHM6IGVycm9yLm1lc3NhZ2VcbiAgICB9LCB7IHN0YXR1czogNTAwIH0pO1xuICB9XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoXG4gIHJlcXVlc3Q6IE5leHRSZXF1ZXN0LFxuICB7IHBhcmFtcyB9OiB7IHBhcmFtczogUHJvbWlzZTx7IGRvY3VtZW50SWQ6IHN0cmluZyB9PiB9XG4pIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZVN1cGFiYXNlU2VydmVyQ2xpZW50T25SZXF1ZXN0KCk7XG4gICAgXG4gICAgLy8gQ2hlY2sgYXV0aGVudGljYXRpb25cbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9LCBlcnJvcjogYXV0aEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcbiAgICBpZiAoYXV0aEVycm9yIHx8ICF1c2VyKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1VuYXV0aG9yaXplZCcgfSwgeyBzdGF0dXM6IDQwMSB9KTtcbiAgICB9XG5cbiAgICBjb25zdCB7IGRvY3VtZW50SWQgfSA9IGF3YWl0IHBhcmFtcztcblxuICAgIGlmICghZG9jdW1lbnRJZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgZXJyb3I6ICdEb2N1bWVudCBJRCBpcyByZXF1aXJlZCdcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDAgfSk7XG4gICAgfVxuXG4gICAgLy8gRmV0Y2ggZG9jdW1lbnQgZGV0YWlsc1xuICAgIGNvbnN0IHsgZGF0YTogZG9jdW1lbnQsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2RvY3VtZW50cycpXG4gICAgICAuc2VsZWN0KGBcbiAgICAgICAgaWQsXG4gICAgICAgIGZpbGVuYW1lLFxuICAgICAgICBmaWxlX3R5cGUsXG4gICAgICAgIGZpbGVfc2l6ZSxcbiAgICAgICAgc3RhdHVzLFxuICAgICAgICBtZXRhZGF0YSxcbiAgICAgICAgY3JlYXRlZF9hdCxcbiAgICAgICAgdXBkYXRlZF9hdFxuICAgICAgYClcbiAgICAgIC5lcSgnaWQnLCBkb2N1bWVudElkKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChlcnJvciB8fCAhZG9jdW1lbnQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IFxuICAgICAgICBlcnJvcjogJ0RvY3VtZW50IG5vdCBmb3VuZCBvciBhY2Nlc3MgZGVuaWVkJyBcbiAgICAgIH0sIHsgc3RhdHVzOiA0MDQgfSk7XG4gICAgfVxuXG4gICAgLy8gR2V0IGNodW5rIGNvdW50XG4gICAgY29uc3QgeyBjb3VudCB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdkb2N1bWVudF9jaHVua3MnKVxuICAgICAgLnNlbGVjdCgnKicsIHsgY291bnQ6ICdleGFjdCcsIGhlYWQ6IHRydWUgfSlcbiAgICAgIC5lcSgnZG9jdW1lbnRfaWQnLCBkb2N1bWVudElkKTtcblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgZG9jdW1lbnQ6IHtcbiAgICAgICAgLi4uZG9jdW1lbnQsXG4gICAgICAgIGNodW5rc19jb3VudDogY291bnQgfHwgMFxuICAgICAgfVxuICAgIH0pO1xuXG4gIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICBjb25zb2xlLmVycm9yKCdbRG9jdW1lbnQgR2V0XSBFcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIGVycm9yOiAnRmFpbGVkIHRvIGZldGNoIGRvY3VtZW50JyxcbiAgICAgIGRldGFpbHM6IGVycm9yLm1lc3NhZ2VcbiAgICB9LCB7IHN0YXR1czogNTAwIH0pO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY3JlYXRlU3VwYWJhc2VTZXJ2ZXJDbGllbnRPblJlcXVlc3QiLCJERUxFVEUiLCJyZXF1ZXN0IiwicGFyYW1zIiwic3VwYWJhc2UiLCJkYXRhIiwidXNlciIsImVycm9yIiwiYXV0aEVycm9yIiwiYXV0aCIsImdldFVzZXIiLCJqc29uIiwic3RhdHVzIiwiZG9jdW1lbnRJZCIsImRvY3VtZW50IiwiZmV0Y2hFcnJvciIsImZyb20iLCJzZWxlY3QiLCJlcSIsImlkIiwic2luZ2xlIiwiY2h1bmtzRXJyb3IiLCJkZWxldGUiLCJjb25zb2xlIiwiZGV0YWlscyIsIm1lc3NhZ2UiLCJkb2NFcnJvciIsInRyYWluaW5nRGF0YUNhY2hlIiwiaW52YWxpZGF0ZSIsImN1c3RvbV9hcGlfY29uZmlnX2lkIiwibG9nIiwid2FybiIsInN1Y2Nlc3MiLCJHRVQiLCJjb3VudCIsImhlYWQiLCJjaHVua3NfY291bnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/documents/[documentId]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute&page=%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdocuments%2F%5BdocumentId%5D%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();